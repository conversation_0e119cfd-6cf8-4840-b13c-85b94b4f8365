<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Teaching Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .topics {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .topics h3 {
            color: #495057;
            margin-bottom: 15px;
            text-align: center;
        }

        .topic-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .topic-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .topic-btn:hover {
            background: #495057;
            transform: translateY(-2px);
        }

        .topic-btn.active {
            background: #007bff;
        }

        .chat-container {
            padding: 30px;
            min-height: 400px;
        }

        .conversation {
            margin-bottom: 30px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            line-height: 1.6;
        }

        .user-message {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .assistant-message {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
            white-space: pre-wrap;
        }

        .input-section {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .question-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .question-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .submit-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1em;
            transition: transform 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Claude Teaching Assistant</h1>
            <p>Your AI tutor for Computer Science fundamentals</p>
        </div>

        <div class="topics">
            <h3>Select a topic (optional):</h3>
            <div class="topic-buttons">
                <button class="topic-btn" data-topic="">General</button>
                <button class="topic-btn" data-topic="data-structures">Data Structures</button>
                <button class="topic-btn" data-topic="algorithms">Algorithms</button>
                <button class="topic-btn" data-topic="oop">Object-Oriented Programming</button>
                <button class="topic-btn" data-topic="complexity">Big O Notation</button>
            </div>
        </div>

        <div class="chat-container">
            <div class="conversation" id="conversation">
                <div class="message assistant-message">
                    Hello! I'm your Computer Science teaching assistant. I specialize in Data Structures & Algorithms and Object-Oriented Programming. 
                    
                    Feel free to ask me questions like:
                    • "Explain how binary search works"
                    • "What's the difference between a stack and a queue?"
                    • "How does inheritance work in OOP?"
                    • "What's the time complexity of quicksort?"
                    
                    What would you like to learn about today?
                </div>
            </div>

            <div class="input-section">
                <input type="text" id="questionInput" class="question-input" 
                       placeholder="Ask your question here..." 
                       onkeypress="handleKeyPress(event)">
                <button id="submitBtn" class="submit-btn" onclick="askQuestion()">Submit</button>
            </div>
        </div>
    </div>

    <script>
        let selectedTopic = '';
        
        // Topic selection
        document.querySelectorAll('.topic-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.topic-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                selectedTopic = this.dataset.topic;
            });
        });

        // Set default topic
        document.querySelector('.topic-btn[data-topic=""]').classList.add('active');

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                askQuestion();
            }
        }

        async function askQuestion() {
            const input = document.getElementById('questionInput');
            const submitBtn = document.getElementById('submitBtn');
            const conversation = document.getElementById('conversation');
            
            const question = input.value.trim();
            if (!question) return;

            // Disable input and show loading
            input.disabled = true;
            submitBtn.disabled = true;
            
            // Add user message
            addMessage(question, 'user');
            input.value = '';

            // Show loading
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading';
            loadingDiv.textContent = 'Thinking...';
            conversation.appendChild(loadingDiv);
            conversation.scrollTop = conversation.scrollHeight;

            try {
                const response = await fetch('/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: question,
                        topic: selectedTopic
                    })
                });

                const data = await response.json();
                
                // Remove loading
                conversation.removeChild(loadingDiv);

                if (response.ok) {
                    addMessage(data.response, 'assistant');
                } else {
                    addError(data.error || 'An error occurred');
                }
            } catch (error) {
                // Remove loading
                conversation.removeChild(loadingDiv);
                addError('Failed to connect to the server');
            }

            // Re-enable input
            input.disabled = false;
            submitBtn.disabled = false;
            input.focus();
        }

        function addMessage(content, sender) {
            const conversation = document.getElementById('conversation');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = content;
            conversation.appendChild(messageDiv);
            conversation.scrollTop = conversation.scrollHeight;
        }

        function addError(message) {
            const conversation = document.getElementById('conversation');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            conversation.appendChild(errorDiv);
            conversation.scrollTop = conversation.scrollHeight;
        }

        // Focus on input when page loads
        document.getElementById('questionInput').focus();
    </script>
</body>
</html>
