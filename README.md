# Claude Teaching Assistant

A Python-based teaching assistant powered by <PERSON>, specializing in Data Structures & Algorithms and Object-Oriented Programming.

## Features

- Interactive web interface for asking questions
- Specialized knowledge in CS fundamentals
- Powered by <PERSON> for intelligent responses
- Clean, simple UI focused on learning

## Setup

1. Clone this repository
2. Install dependencies: `pip install -r requirements.txt`
3. Copy `.env.example` to `.env` and add your Claude API key
4. Run the application: `python app.py`
5. Open your browser to `http://localhost:5000`

## Topics Covered

- **Data Structures & Algorithms**: Arrays, linked lists, trees, graphs, sorting, searching, complexity analysis
- **Object-Oriented Programming**: Classes, inheritance, polymorphism, encapsulation, design patterns

## Usage

Simply type your question in the text box and press Enter or click Submit. The teaching assistant will provide detailed explanations, examples, and guidance.
