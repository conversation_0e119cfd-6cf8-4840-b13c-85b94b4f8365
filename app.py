from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import os
from dotenv import load_dotenv
import anthropic
import logging

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Claude client
try:
    client = anthropic.Anthropic(api_key=os.getenv('ANTHROPIC_API_KEY'))
    logger.info("Claude client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Claude client: {e}")
    client = None

class TeachingAssistant:
    def __init__(self):
        self.system_prompt = """You are an expert Computer Science teaching assistant specializing in:
1. Data Structures & Algorithms (arrays, linked lists, trees, graphs, sorting, searching, Big O notation)
2. Object-Oriented Programming (classes, inheritance, polymorphism, encapsulation, design patterns)

Your teaching style:
- Provide clear, step-by-step explanations
- Use concrete examples and analogies
- Include code examples when helpful
- Break down complex concepts into digestible parts
- Encourage understanding over memorization
- Ask clarifying questions when the student's question is unclear
- Provide practice problems when appropriate

Always be patient, encouraging, and focus on helping students truly understand the concepts."""

    def get_response(self, question, topic_context=""):
        if not client:
            return "Sorry, the teaching assistant is currently unavailable. Please check the API configuration."
        
        try:
            # Enhance the question with topic context if provided
            enhanced_question = f"{topic_context}\n\nStudent question: {question}" if topic_context else question
            
            message = client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=1000,
                temperature=0.7,
                system=self.system_prompt,
                messages=[
                    {"role": "user", "content": enhanced_question}
                ]
            )
            
            return message.content[0].text
            
        except Exception as e:
            logger.error(f"Error getting Claude response: {e}")
            return f"I apologize, but I encountered an error while processing your question. Please try again."

# Initialize teaching assistant
ta = TeachingAssistant()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/ask', methods=['POST'])
def ask_question():
    try:
        data = request.get_json()
        question = data.get('question', '').strip()
        topic = data.get('topic', '')
        
        if not question:
            return jsonify({'error': 'Please provide a question'}), 400
        
        # Get response from Claude
        response = ta.get_response(question, topic)
        
        return jsonify({
            'response': response,
            'question': question
        })
        
    except Exception as e:
        logger.error(f"Error in ask_question: {e}")
        return jsonify({'error': 'An error occurred while processing your question'}), 500

@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy', 'claude_available': client is not None})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
